/* Mobile-First Booking Calendar Styles */

/* Booking Container */
.booking-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
  min-height: calc(100vh - 120px);
}

/* Mobile-First Header */
.booking-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--gradient-light);
  border-radius: var(--radius-xl);
  border: 1px solid var(--gray-200);
}

.booking-title {
  font-size: 2rem;
  font-weight: 800;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  letter-spacing: -0.025em;
}

.booking-subtitle {
  color: var(--gray-600);
  font-size: 1.1rem;
  font-weight: 500;
  margin: 0;
}

/* Mobile Date Selector */
.mobile-date-selector {
  margin-bottom: 2rem;
}

.date-navigation {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  background: var(--white);
  padding: 1rem;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-200);
}

.date-nav-btn {
  background: var(--gradient-primary);
  color: var(--white);
  border: none;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  justify-content: center;
}

.date-nav-btn:hover:not(:disabled) {
  transform: scale(1.1);
  box-shadow: var(--shadow-xl);
}

.date-nav-btn:disabled {
  background: var(--gray-300);
  cursor: not-allowed;
  opacity: 0.5;
}

.current-date {
  position: relative;
  cursor: pointer;
}

.date-display {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  background: var(--gray-50);
  border-radius: var(--radius-lg);
  border: 2px solid var(--gray-200);
  transition: all 0.3s ease;
}

.date-display:hover {
  border-color: var(--primary-red);
  background: var(--white);
}

.date-text {
  font-weight: 600;
  color: var(--gray-900);
  font-size: 1.1rem;
}

.date-input-hidden {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

/* Loading State */
.loading-state {
  text-align: center;
  padding: 3rem;
  color: var(--gray-600);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--gray-200);
  border-top: 4px solid var(--primary-red);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Courts Container */
.courts-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Court Card */
.court-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-200);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.court-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-2xl);
}

.court-header {
  background: var(--gradient-tennis);
  color: var(--white);
  padding: 1.5rem;
  text-align: center;
}

.court-name {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  letter-spacing: -0.025em;
}

.court-description {
  font-size: 0.95rem;
  opacity: 0.9;
  font-weight: 500;
}

/* Time Slots Grid */
.time-slots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
  padding: 1.5rem;
}

/* Time Slot */
.time-slot {
  background: var(--gray-50);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: 1.25rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.time-slot:not(.booked):not(.past):hover {
  transform: translateY(-2px);
  border-color: var(--primary-red);
  box-shadow: var(--shadow-lg);
  background: var(--white);
}

.time-slot.booked {
  background: var(--gradient-primary);
  border-color: transparent;
  color: var(--white);
  cursor: default;
}

.time-slot.past {
  background: var(--gray-200);
  border-color: var(--gray-300);
  color: var(--gray-500);
  cursor: not-allowed;
  opacity: 0.6;
}

.time-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.time-text {
  font-weight: 700;
  font-size: 1.1rem;
}

.booking-info {
  text-align: center;
}

.booking-status {
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.booking-players {
  font-size: 0.85rem;
  opacity: 0.9;
  font-weight: 500;
}

.slot-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-weight: 600;
  font-size: 0.95rem;
}

.available-status {
  color: var(--tennis-green);
}

.past-status {
  color: var(--gray-500);
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
  .booking-container {
    padding: 0.75rem;
  }
  
  .booking-title {
    font-size: 1.75rem;
  }
  
  .booking-subtitle {
    font-size: 1rem;
  }
  
  .time-slots-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
    padding: 1rem;
  }
  
  .time-slot {
    min-height: 80px;
    padding: 1rem;
  }
  
  .court-header {
    padding: 1.25rem;
  }
  
  .court-name {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  .date-navigation {
    padding: 0.75rem;
    gap: 0.75rem;
  }

  .date-nav-btn {
    width: 44px;
    height: 44px;
    font-size: 1.1rem;
  }

  .date-text {
    font-size: 1rem;
  }

  .time-slot {
    min-height: 70px;
    padding: 0.875rem;
  }

  .time-text {
    font-size: 1rem;
  }
}

/* Mobile-Optimized Booking Modal */
.booking-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.booking-modal {
  background: var(--white);
  width: 100%;
  max-width: 500px;
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
  box-shadow: var(--shadow-2xl);
  animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  max-height: 90vh;
  overflow-y: auto;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 1.5rem 1rem;
  border-bottom: 1px solid var(--gray-200);
  background: var(--gradient-light);
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--gray-900);
  margin: 0;
  letter-spacing: -0.025em;
}

.modal-close-btn {
  background: var(--gray-100);
  border: none;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  cursor: pointer;
  color: var(--gray-600);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close-btn:hover {
  background: var(--gray-200);
  color: var(--gray-900);
  transform: scale(1.05);
}

/* Booking Summary */
.booking-summary {
  padding: 1.5rem;
  background: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--gray-200);
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-label {
  font-weight: 600;
  color: var(--gray-700);
  font-size: 0.95rem;
}

.summary-value {
  font-weight: 700;
  color: var(--gray-900);
  font-size: 1rem;
}

/* Booking Form */
.booking-form {
  padding: 1.5rem;
}

.mobile-input {
  font-size: 16px; /* Prevents zoom on iOS */
  padding: 1.25rem 1.5rem;
  border-radius: var(--radius-xl);
  border: 2px solid var(--gray-300);
  transition: all 0.3s ease;
  background: var(--white);
}

.mobile-input:focus {
  border-color: var(--primary-red);
  box-shadow: 0 0 0 4px rgba(220, 38, 38, 0.1);
  transform: translateY(-1px);
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: var(--gray-800);
  font-size: 1rem;
}

/* Modal Actions */
.modal-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--gray-200);
}

.mobile-btn {
  flex: 1;
  padding: 1.25rem 1.5rem;
  font-size: 1.1rem;
  font-weight: 700;
  border-radius: var(--radius-xl);
  min-height: 56px;
  letter-spacing: 0.025em;
}

/* Desktop Modal Adjustments */
@media (min-width: 769px) {
  .booking-modal-overlay {
    align-items: center;
  }

  .booking-modal {
    border-radius: var(--radius-2xl);
    max-height: 80vh;
    width: 90%;
    max-width: 500px;
  }

  .mobile-input {
    font-size: 1rem;
  }
}
