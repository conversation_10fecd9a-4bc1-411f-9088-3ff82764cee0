# TC-Wehen Tennis Club Website

Eine moderne Website für den TC-Wehen Tennisclub mit integriertem Buchungssystem.

## 🎾 Features

- **Responsive Design** - Optimiert für Desktop, Tablet und Mobile
- **Platz-Buchungssystem** - Online-Buchung der 6 Tennisplätze
- **Benutzer-Authentifizierung** - Registrierung und Login für Vereinsmitglieder
- **Admin-Panel** - Verwaltung von Buchungen und Benutzern
- **Mobile-First** - Touch-optimierte Bedienung

## 🏗️ Technologie-Stack

### Frontend
- **React** mit Vite
- **Vanilla CSS** mit CSS Grid und Flexbox
- **Responsive Design** mit Mobile-First Ansatz

### Backend
- **Node.js** mit Express
- **Supabase** als Datenbank und Authentifizierung
- **RESTful API** für alle Operationen

### Deployment
- **Render** für Frontend und Backend Hosting
- **Supabase** für Datenbank-Hosting

## 📁 Projekt-Struktur

```
TC-Wehen Website/
├── tc-wehen-frontend/         # Frontend (React/Vite) - PRODUCTION
│   ├── src/
│   ├── public/assets/         # Bilder und Assets
│   └── package.json
├── tc-wehen-backend/          # Backend (Node.js/Express)
│   ├── routes/               # API Routes
│   ├── middleware/           # Auth Middleware
│   ├── config/              # Supabase Config
│   └── server.js
└── archive/                  # Archivierte Versionen
    └── tc-wehen-website/     # Legacy Vanilla JS Version
```

## 🚀 Lokale Entwicklung

### Frontend starten
```bash
cd tc-wehen-frontend
npm install
npm run dev
```

### Backend starten
```bash
cd tc-wehen-backend
npm install
# .env Datei mit Supabase-Daten erstellen
npm run dev
```

## 🌐 Live-Website

- **Frontend**: https://tc-wehen-frontend.onrender.com/
- **Backend API**: [Wird auf Render deployed]
- **Datenbank**: Supabase

## 👥 Entwickelt für

TC-Wehen Tennisclub - Ein traditioneller Dorfverein mit modernen digitalen Lösungen.

## 📱 Mobile Features

- Touch-optimierte Bedienung
- Swipe-Gesten für Modals
- Bottom-Sheet Design
- Stabile Hover-Effekte ohne Layout-Verschiebungen
- Responsive Design für alle Bildschirmgrößen

## 🔄 Projekt-Reorganisation

**Stand:** 01.06.2025

Das Projekt wurde reorganisiert, um Duplikate zu eliminieren und eine klare Struktur zu schaffen:

- ✅ **Konsolidierung:** Vanilla JS Version in `archive/` verschoben
- ✅ **Hauptversion:** React-Frontend (`tc-wehen-frontend/`) als einzige aktive Version
- ✅ **Asset-Bereinigung:** Doppelte Bilder entfernt
- ✅ **Deployment:** Bestehende Render-Konfiguration bleibt unverändert
- ✅ **Funktionalität:** Alle Features und Verbesserungen erhalten
