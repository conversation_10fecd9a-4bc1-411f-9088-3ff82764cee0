const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(helmet());
app.use(cors({
  origin: [
    'http://localhost:5173',
    'https://tc-wehen-frontend.onrender.com'
  ],
  credentials: true
}));
app.use(morgan('combined'));
app.use(express.json());

// Health check first
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'TC-<PERSON><PERSON>end is running!' });
});

// Test route
app.get('/api/test', (req, res) => {
  res.json({ message: 'API is working!' });
});

// Import and use routes one by one to isolate the problem
console.log('🔍 Loading routes...');

try {
  const authRoutes = require('./routes/auth');
  console.log('✅ Auth routes imported');
  app.use('/api/auth', authRoutes);
  console.log('✅ Auth routes mounted');
} catch (error) {
  console.error('❌ Error with auth routes:', error.message);
}

try {
  const courtsRoutes = require('./routes/courts');
  console.log('✅ Courts routes imported');
  app.use('/api/courts', courtsRoutes);
  console.log('✅ Courts routes mounted');
} catch (error) {
  console.error('❌ Error with courts routes:', error.message);
}

try {
  const usersRoutes = require('./routes/users');
  console.log('✅ Users routes imported');
  app.use('/api/users', usersRoutes);
  console.log('✅ Users routes mounted');
} catch (error) {
  console.error('❌ Error with users routes:', error.message);
}

try {
  const bookingsRoutes = require('./routes/bookings');
  console.log('✅ Bookings routes imported');
  app.use('/api/bookings', bookingsRoutes);
  console.log('✅ Bookings routes mounted');
} catch (error) {
  console.error('❌ Error with bookings routes:', error.message);
}

console.log('🔍 All routes processed, starting server...');

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Global error handler:', err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

// 404 handler - Korrekte Syntax
app.use((req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

app.listen(PORT, () => {
  console.log(`🚀 TC-Wehen Backend running on port ${PORT}`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV}`);
});

module.exports = app;
