import { useState } from 'react'
import { AuthProvider, useAuth } from './contexts/AuthContext'
import LandingPage from './components/LandingPage'
import Header from './components/Header'
import Login from './components/Login'
import Register from './components/Register'
import BookingCalendar from './components/BookingCalendar'

function AuthPage() {
  const [isLogin, setIsLogin] = useState(true)

  return (
    <div style={{
      minHeight: '100vh',
      backgroundImage: "url('/assets/Terrassenansicht-Plätze.PNG')",
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '2rem 1rem',
      position: 'relative'
    }}>
      {/* Overlay for better form readability */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'rgba(0, 0, 0, 0.3)',
        zIndex: 1
      }}></div>

      <div style={{ position: 'relative', zIndex: 2 }}>
        {isLogin ? (
          <Login onToggleMode={() => setIsLogin(false)} />
        ) : (
          <Register onToggleMode={() => setIsLogin(true)} />
        )}
      </div>
    </div>
  )
}

function Dashboard({ onNavigateHome }) {
  const handleNavigate = (page) => {
    if (page === 'home') {
      onNavigateHome()
    }
  }

  return (
    <div style={{ minHeight: '100vh', background: 'var(--gray-50)' }}>
      <Header onNavigate={handleNavigate} />
      <BookingCalendar />
    </div>
  )
}

function AppContent() {
  const { user, loading, showBooking, setShowBooking } = useAuth()

  if (loading) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundImage: "url('/assets/Plätze.PNG')",
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        position: 'relative'
      }}>
        {/* Overlay for better text readability */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0, 0, 0, 0.4)',
          zIndex: 1
        }}></div>

        <div className="text-center" style={{ position: 'relative', zIndex: 2 }}>
          <div style={{
            width: '120px',
            height: '120px',
            background: 'rgba(255, 255, 255, 0.95)',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 2rem',
            animation: 'pulse 2s infinite',
            boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',
            backdropFilter: 'blur(10px)',
            padding: '10px'
          }}>
            <img
              src="/assets/Logo-TCW.PNG"
              alt="TC-Wehen Logo"
              style={{
                width: '90px',
                height: '90px',
                objectFit: 'contain'
              }}
            />
          </div>
          <h2 style={{
            color: 'white',
            marginBottom: '1rem',
            fontSize: '1.8rem',
            fontWeight: 'bold',
            textShadow: '2px 2px 4px rgba(0, 0, 0, 0.7)'
          }}>
            Willkommen zurück
          </h2>
          <p style={{
            color: 'rgba(255, 255, 255, 0.9)',
            fontSize: '1.1rem',
            textShadow: '1px 1px 2px rgba(0, 0, 0, 0.7)'
          }}>
            Lade...
          </p>
        </div>
      </div>
    )
  }

  // Show booking page only if user is logged in and booking is requested
  if (showBooking && user) {
    return <Dashboard onNavigateHome={() => setShowBooking(false)} />
  }

  // Show login page only if booking is requested but user is not logged in
  if (showBooking && !user) {
    return <AuthPage />
  }

  // Show public landing page by default
  return <LandingPage onBookingClick={() => setShowBooking(true)} />
}

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  )
}

export default App
