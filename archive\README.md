# Archive - TC-Wehen Website Legacy Versions

## 📁 Inhalt

Dieses Verzeichnis enthält archivierte Versionen des TC-Wehen Website Projekts, die nicht mehr aktiv entwickelt werden, aber zur Referenz aufbewahrt werden.

## 🗂️ Archivierte Projekte

### `tc-wehen-website/` - Vanilla JS Version (Legacy)

**Archiviert am:** 01.06.2025  
**Grund:** Konsolidierung auf React-basierte Lösung

**Beschreibung:**
- Einfache Vanilla JavaScript/Vite Anwendung
- LocalStorage-basiertes Buchungssystem
- Keine Authentifizierung
- Grundlegende mobile Optimierung

**Technologie-Stack:**
- Vanilla JavaScript
- Vite Build Tool
- CSS3
- LocalStorage für Datenpersistierung

**Warum archiviert:**
1. **Doppelte Funktionalität:** Die React-Version (`tc-wehen-frontend`) bietet alle Funktionen und mehr
2. **Bessere Architektur:** React-Version hat bessere Code-Organisation und Wartbarkeit
3. **Moderne Features:** React-Version hat Supabase-Integration, Authentifizierung, und bessere mobile UX
4. **Deployment:** React-Version ist bereits produktiv auf Render deployed
5. **Zukunftssicherheit:** React-Stack ist besser für zukünftige Erweiterungen geeignet

## 🚀 Aktuelle Produktionsversion

Die aktuelle, produktive Version befindet sich in:
- **Verzeichnis:** `../tc-wehen-frontend/`
- **Live-URL:** https://tc-wehen-frontend.onrender.com/
- **Technologie:** React + Vite + Supabase

## 📋 Wiederherstellung

Falls die archivierte Version wiederhergestellt werden soll:

```bash
# Aus dem Hauptverzeichnis
cp -r archive/tc-wehen-website ./tc-wehen-website-restored
cd tc-wehen-website-restored
npm install
npm run dev
```

## 🔄 Migrationsstatus

- ✅ Alle Assets wurden in die React-Version übertragen
- ✅ Alle Funktionalitäten wurden in die React-Version implementiert
- ✅ Mobile Optimierungen wurden verbessert
- ✅ Deutsche Sprache wurde beibehalten
- ✅ Design und Styling wurden modernisiert
- ✅ Hover-Effekte wurden stabilisiert

## 📝 Hinweise

- Diese archivierte Version sollte **nicht** für neue Entwicklungen verwendet werden
- Alle neuen Features und Bugfixes werden nur in der React-Version (`tc-wehen-frontend`) implementiert
- Die Archivierung dient nur der Dokumentation und als Backup
