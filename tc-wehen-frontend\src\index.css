:root {
  /* Clean Typography Stack - Minimalist approach */
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Aria<PERSON>, sans-serif;
  line-height: 1.6;
  font-weight: 400;

  /* TC <PERSON>hen Refined Brand Colors - Strategic and sophisticated */
  --tc-yellow-orange: #ff6b35;  /* Primary TC-Wehen warm tone */
  --tc-red: #d32f2f;            /* Secondary TC-Wehen accent */
  --tc-blue: #1e40af;           /* Complementary blue for balance */

  /* Refined accent variations for subtle use */
  --tc-yellow-orange-light: #ff8c5a;
  --tc-yellow-orange-dark: #e55a2b;
  --tc-red-light: #ef4444;
  --tc-blue-light: #3b82f6;

  /* Minimalist Neutral Palette */
  --white: #ffffff;
  --gray-50: #fafafa;
  --gray-100: #f5f5f5;
  --gray-200: #e5e5e5;
  --gray-300: #d4d4d4;
  --gray-400: #a3a3a3;
  --gray-500: #737373;
  --gray-600: #525252;
  --gray-700: #404040;
  --gray-800: #262626;
  --gray-900: #171717;
  --black: #000000;

  /* Minimalist Backgrounds */
  --bg-primary: var(--white);
  --bg-secondary: var(--gray-50);
  --bg-accent: var(--gray-100);

  /* Clean Shadows - Subtle and minimal */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Minimal Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;

  color: var(--gray-900);
  background-color: var(--white);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background: var(--white);
  scroll-behavior: smooth;
}

/* Clean Typography Hierarchy */
h1, h2, h3, h4, h5, h6 {
  color: var(--gray-900);
  font-weight: 600;
  letter-spacing: -0.025em;
  line-height: 1.3;
  margin: 0;
}

h1 {
  font-size: 2.25rem;
  font-weight: 700;
}

h2 {
  font-size: 1.875rem;
  font-weight: 600;
}

h3 {
  font-size: 1.5rem;
  font-weight: 600;
}

h4 {
  font-size: 1.25rem;
  font-weight: 500;
}

/* Clean Link Styles */
a {
  font-weight: 500;
  color: var(--tc-yellow-orange);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--tc-yellow-orange-dark);
}

a:focus {
  outline: 2px solid var(--tc-yellow-orange);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Clean Button System */
.btn {
  border-radius: var(--radius-lg);
  border: none;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-transform: none;
  letter-spacing: 0;
}

.btn-primary {
  background: var(--tc-yellow-orange);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  background: var(--tc-yellow-orange-dark);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--white);
  color: var(--tc-yellow-orange);
  border: 1px solid var(--gray-300);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background: var(--gray-50);
  border-color: var(--tc-yellow-orange);
}

.btn-outline {
  background-color: transparent;
  color: var(--gray-700);
  border: 1px solid var(--gray-300);
}

.btn-outline:hover {
  background-color: var(--gray-50);
  border-color: var(--gray-400);
  color: var(--gray-900);
}

/* Clean Card System */
.card {
  background: var(--white);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: box-shadow 0.2s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-compact {
  padding: 1rem;
  border-radius: var(--radius-md);
}

.card-elevated {
  box-shadow: var(--shadow-lg);
}

/* Modern Container System */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.container-sm {
  max-width: 640px;
}

.container-lg {
  max-width: 1400px;
}

/* Clean Form System */
.form-group {
  margin-bottom: 1.25rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--gray-700);
  font-size: 0.875rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: 1rem;
  font-family: inherit;
  transition: border-color 0.2s ease;
  background-color: var(--white);
}

.form-input:focus {
  outline: none;
  border-color: var(--tc-yellow-orange);
  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.form-input:hover {
  border-color: var(--gray-400);
}

.form-input::placeholder {
  color: var(--gray-500);
}

.text-center {
  text-align: center;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mt-4 {
  margin-top: 1rem;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .md\\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .hidden {
    display: none;
  }

  .md\\:flex {
    display: flex;
  }
}

@media (max-width: 767px) {
  .md\\:hidden {
    display: none;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* Mobile optimizations for loading and auth screens */
@media (max-width: 768px) {
  /* Loading screen mobile */
  .loading-screen {
    padding: 1rem;
  }

  .loading-logo {
    width: 100px !important;
    height: 100px !important;
  }

  .loading-logo img {
    width: 75px !important;
    height: 75px !important;
  }

  .loading-title {
    font-size: 1.5rem !important;
  }

  /* Auth page mobile */
  .card {
    margin: 1rem !important;
    padding: 1.5rem !important;
    max-width: 350px !important;
  }

  .auth-logo {
    width: 70px !important;
    height: 70px !important;
  }

  .auth-logo img {
    width: 50px !important;
    height: 50px !important;
  }
}

@media (max-width: 480px) {
  .loading-screen {
    padding: 0.5rem;
  }

  .loading-logo {
    width: 80px !important;
    height: 80px !important;
    margin-bottom: 1.5rem !important;
  }

  .loading-logo img {
    width: 60px !important;
    height: 60px !important;
  }

  .loading-title {
    font-size: 1.3rem !important;
  }

  .card {
    margin: 0.5rem !important;
    padding: 1rem !important;
    max-width: 320px !important;
  }
}
