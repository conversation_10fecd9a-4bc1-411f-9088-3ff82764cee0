:root {
  /* Modern Typography Stack */
  font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Aria<PERSON>, sans-serif;
  line-height: 1.6;
  font-weight: 400;

  /* TC Wehen Modern Color Palette - Enhanced for better contrast and accessibility */
  --primary-red: #dc2626;
  --primary-orange: #ea580c;
  --accent-red: #ef4444;
  --accent-orange: #f97316;
  --logo-blue: #2563eb;
  --logo-green: #059669;
  --tennis-green: #16a34a;

  /* Neutral Colors - Warmer and more sophisticated */
  --white: #ffffff;
  --gray-50: #fafaf9;
  --gray-100: #f5f5f4;
  --gray-200: #e7e5e4;
  --gray-300: #d6d3d1;
  --gray-400: #a8a29e;
  --gray-500: #78716c;
  --gray-600: #57534e;
  --gray-700: #44403c;
  --gray-800: #292524;
  --gray-900: #1c1917;

  /* Modern Gradients - More sophisticated and balanced */
  --gradient-primary: linear-gradient(135deg, var(--primary-red) 0%, var(--primary-orange) 60%, var(--logo-blue) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-red) 0%, var(--accent-orange) 100%);
  --gradient-light: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
  --gradient-tennis: linear-gradient(135deg, var(--tennis-green) 0%, var(--logo-green) 100%);

  /* Modern Shadows - Layered and sophisticated */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Border Radius System */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  color: var(--gray-900);
  background-color: var(--gray-50);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background: var(--gradient-light);
  scroll-behavior: smooth;
}

/* Modern Typography Hierarchy */
h1, h2, h3, h4, h5, h6 {
  color: var(--gray-900);
  font-weight: 700;
  letter-spacing: -0.025em;
  line-height: 1.2;
}

h1 {
  font-size: 2.5rem;
  font-weight: 800;
}

h2 {
  font-size: 2rem;
  font-weight: 700;
}

h3 {
  font-size: 1.5rem;
  font-weight: 600;
}

h4 {
  font-size: 1.25rem;
  font-weight: 600;
}

/* Modern Link Styles */
a {
  font-weight: 500;
  color: var(--primary-red);
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

a:hover {
  color: var(--accent-red);
  transform: translateY(-1px);
}

a:focus {
  outline: 2px solid var(--primary-red);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Modern Button System */
.btn {
  border-radius: var(--radius-lg);
  border: none;
  padding: 0.875rem 1.75rem;
  font-size: 1rem;
  font-weight: 600;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
  text-transform: none;
  letter-spacing: 0.025em;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  background: linear-gradient(135deg, var(--accent-red) 0%, var(--accent-orange) 60%, var(--logo-blue) 100%);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--white);
  color: var(--primary-red);
  border: 2px solid var(--primary-red);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background: var(--gradient-accent);
  color: white;
  border-color: transparent;
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-outline {
  background-color: transparent;
  color: var(--gray-700);
  border: 2px solid var(--gray-300);
  box-shadow: none;
}

.btn-outline:hover {
  background-color: var(--gray-100);
  border-color: var(--gray-400);
  color: var(--gray-900);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.btn-tennis {
  background: var(--gradient-tennis);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-tennis:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

/* Modern Card System */
.card {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: 2rem;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-200);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
}

.card:hover::before {
  opacity: 1;
}

.card-compact {
  padding: 1.5rem;
  border-radius: var(--radius-lg);
}

.card-elevated {
  box-shadow: var(--shadow-xl);
}

/* Modern Container System */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.container-sm {
  max-width: 640px;
}

.container-lg {
  max-width: 1400px;
}

/* Modern Form System */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: var(--gray-800);
  font-size: 0.95rem;
  letter-spacing: 0.025em;
}

.form-input {
  width: 100%;
  padding: 1rem 1.25rem;
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-lg);
  font-size: 1rem;
  font-family: inherit;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background-color: var(--white);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-red);
  box-shadow: 0 0 0 4px rgba(220, 38, 38, 0.1);
  transform: translateY(-1px);
}

.form-input:hover {
  border-color: var(--gray-400);
}

.form-input::placeholder {
  color: var(--gray-500);
}

.text-center {
  text-align: center;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mt-4 {
  margin-top: 1rem;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .md\\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .hidden {
    display: none;
  }

  .md\\:flex {
    display: flex;
  }
}

@media (max-width: 767px) {
  .md\\:hidden {
    display: none;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* Mobile optimizations for loading and auth screens */
@media (max-width: 768px) {
  /* Loading screen mobile */
  .loading-screen {
    padding: 1rem;
  }

  .loading-logo {
    width: 100px !important;
    height: 100px !important;
  }

  .loading-logo img {
    width: 75px !important;
    height: 75px !important;
  }

  .loading-title {
    font-size: 1.5rem !important;
  }

  /* Auth page mobile */
  .card {
    margin: 1rem !important;
    padding: 1.5rem !important;
    max-width: 350px !important;
  }

  .auth-logo {
    width: 70px !important;
    height: 70px !important;
  }

  .auth-logo img {
    width: 50px !important;
    height: 50px !important;
  }
}

@media (max-width: 480px) {
  .loading-screen {
    padding: 0.5rem;
  }

  .loading-logo {
    width: 80px !important;
    height: 80px !important;
    margin-bottom: 1.5rem !important;
  }

  .loading-logo img {
    width: 60px !important;
    height: 60px !important;
  }

  .loading-title {
    font-size: 1.3rem !important;
  }

  .card {
    margin: 0.5rem !important;
    padding: 1rem !important;
    max-width: 320px !important;
  }
}
