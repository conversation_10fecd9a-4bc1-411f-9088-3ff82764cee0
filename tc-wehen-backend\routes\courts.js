const express = require('express');
const { supabase } = require('../config/supabase');

const router = express.Router();

// Alle Plätze abrufen
router.get('/', async (req, res) => {
  try {
    const { data: courts, error } = await supabase
      .from('courts')
      .select('*')
      .eq('is_active', true)
      .order('id');

    if (error) {
      return res.status(400).json({ error: error.message });
    }

    res.json(courts);
  } catch (error) {
    console.error('Get courts error:', error);
    res.status(500).json({ error: 'Failed to fetch courts' });
  }
});

// Einzelnen Platz abrufen
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const { data: court, error } = await supabase
      .from('courts')
      .select('*')
      .eq('id', id)
      .eq('is_active', true)
      .single();

    if (error) {
      return res.status(404).json({ error: 'Court not found' });
    }

    res.json(court);
  } catch (error) {
    console.error('Get court error:', error);
    res.status(500).json({ error: 'Failed to fetch court' });
  }
});

module.exports = router;
