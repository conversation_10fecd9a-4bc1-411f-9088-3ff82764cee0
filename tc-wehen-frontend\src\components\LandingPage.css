/* TC-<PERSON>hen Minimalist Landing Page Styles */

.landing-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: var(--gray-900);
  background: var(--white);
}

/* Clean Header Styles */
.header {
  background: var(--white);
  color: var(--gray-900);
  padding: 1rem 0;
  box-shadow: var(--shadow-sm);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  transition: all 0.3s ease;
  border-bottom: 1px solid var(--gray-200);
}

.header-visible {
  transform: translateY(0);
  opacity: 1;
}

.header-hidden {
  transform: translateY(-100%);
  opacity: 0.95;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
}

.logo {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: var(--gray-900);
  flex-shrink: 0;
  text-decoration: none;
}

.logo-img {
  height: 50px;
  width: 50px;
  border-radius: 50%;
  object-fit: contain;
  background: var(--gray-50);
  padding: 4px;
  border: 1px solid var(--gray-200);
}

.logo h1 {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  color: var(--gray-900);
  letter-spacing: -0.025em;
}

/* Clean Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 32px;
  height: 32px;
  background: var(--white);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  cursor: pointer;
  padding: 4px;
  z-index: 1001;
  transition: all 0.2s ease;
}

.mobile-menu-toggle:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
}

.hamburger-line {
  width: 20px;
  height: 2px;
  background-color: var(--gray-700);
  border-radius: 1px;
  transition: all 0.3s ease;
  transform-origin: center;
}

.hamburger-line.open:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.hamburger-line.open:nth-child(2) {
  opacity: 0;
  transform: scale(0);
}

.hamburger-line.open:nth-child(3) {
  transform: rotate(-45deg) translate(5px, -5px);
}

/* Clean Navigation Styles */
.nav {
  display: flex;
}

/* Desktop Navigation */
.desktop-nav {
  display: flex;
}

/* Mobile Navigation - Hidden by default */
.mobile-nav {
  display: none;
}

.nav-list {
  display: flex;
  list-style: none;
  gap: 0.5rem;
  align-items: center;
  margin: 0;
  padding: 0;
}

.nav-list a {
  color: var(--gray-700);
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
  cursor: pointer;
  font-size: 0.95rem;
}

.nav-list a:hover,
.nav-list a.active {
  color: var(--gray-900);
  background: var(--gray-100);
}

/* Clean Dropdown Styles */
.dropdown {
  position: relative;
}

.dropdown-arrow {
  font-size: 0.75rem;
  margin-left: 0.5rem;
  transition: transform 0.2s ease;
}

.dropdown-open .dropdown-arrow {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: var(--white);
  border: 1px solid var(--gray-200);
  min-width: 200px;
  box-shadow: var(--shadow-lg);
  border-radius: var(--radius-lg);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-8px);
  transition: all 0.2s ease;
  z-index: 1000;
  overflow: hidden;
}

.dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-menu li {
  list-style: none;
}

.dropdown-menu a {
  color: var(--gray-700) !important;
  display: block;
  padding: 0.75rem 1rem;
  font-weight: 500;
  font-size: 0.9rem;
  text-decoration: none;
  transition: all 0.2s ease;
  border-bottom: 1px solid var(--gray-100);
}

.dropdown-menu a:last-child {
  border-bottom: none;
}

.dropdown-menu a:hover {
  background-color: var(--gray-50);
  color: var(--gray-900) !important;
}

/* Clean Booking Button Styling */
.booking-button {
  background: var(--tc-yellow-orange) !important;
  color: var(--white) !important;
  font-weight: 600 !important;
  box-shadow: var(--shadow-sm) !important;
  border: none !important;
  border-radius: var(--radius-md) !important;
  padding: 0.75rem 1.5rem !important;
}

.booking-button:hover {
  background: var(--tc-yellow-orange-dark) !important;
  box-shadow: var(--shadow-md) !important;
}

/* Main Content */
.main {
  flex: 1;
  padding-top: 70px; /* Account for fixed header */
}

/* Clean Page Layout */
.page {
  min-height: calc(100vh - 70px);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  padding: 3rem 0;
}

.page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  z-index: 1;
}

/* Clean Content Container */
.content {
  position: relative;
  z-index: 2;
  max-width: 1000px;
  margin: 0 auto;
  padding: 3rem;
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
}

/* Clean Home Page */
.home-content {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 4rem;
}

.welcome-message {
  text-align: center;
  background: var(--white);
  padding: 4rem 2rem;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  border-top: 3px solid var(--tc-yellow-orange);
}

.welcome-message h2 {
  font-size: 3rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 1rem;
  letter-spacing: -0.025em;
  line-height: 1.2;
}

.welcome-subtitle {
  font-size: 1.25rem;
  color: var(--gray-600);
  margin: 0;
  font-weight: 400;
  line-height: 1.5;
}

/* Clean News Section */
.news-section {
  background: var(--white);
  padding: 3rem 2rem;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  border-top: 3px solid var(--tc-blue);
}

.news-section h3 {
  color: var(--gray-900);
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 2rem;
  text-align: center;
  letter-spacing: -0.025em;
}

.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.news-item {
  background: var(--gray-50);
  padding: 1.5rem;
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
  transition: box-shadow 0.2s ease;
}

.news-item:hover {
  box-shadow: var(--shadow-md);
}

.news-date {
  color: var(--tc-yellow-orange);
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.news-item h4 {
  color: var(--gray-900);
  font-size: 1.25rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  letter-spacing: -0.025em;
  line-height: 1.3;
}

.news-item p {
  color: var(--gray-700);
  line-height: 1.6;
  margin: 0;
  font-size: 0.95rem;
}

/* Clean Page Styles */
.training-content {
  text-align: center;
}

.training-logo {
  max-width: 300px;
  height: auto;
  margin-bottom: 2rem;
}

/* Page Headers */
.verein-content h2,
.anlage-content h2,
.kontakt-content h2,
.impressum-content h2,
.datenschutz-content h2,
.placeholder-content h2 {
  color: var(--gray-900);
  margin-bottom: 2rem;
  text-align: center;
  font-size: 2rem;
  font-weight: 600;
}

/* Page Content */
.verein-content p,
.kontakt-info p,
.impressum-info p,
.datenschutz-info p,
.placeholder-info p {
  margin-bottom: 1rem;
  font-size: 1rem;
  line-height: 1.6;
  color: var(--gray-700);
}

/* Anlage Images */
.anlage-images {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.anlage-images img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
}

/* Google Maps Styles */
.google-maps {
  margin-top: 2rem;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
}

.google-maps iframe {
  width: 100%;
  height: 300px;
  border: none;
  border-radius: var(--radius-md);
}

.map-info {
  background: var(--gray-50);
  padding: 1rem;
  text-align: center;
  border-top: 1px solid var(--gray-200);
}

.map-description {
  margin: 0;
  color: var(--gray-700);
  font-size: 0.95rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

/* Contact Info */
.kontakt-info {
  margin-bottom: 2rem;
}

.kontakt-info h3,
.impressum-info h3,
.datenschutz-info h3 {
  color: var(--tc-yellow-orange);
  margin: 1.5rem 0 1rem 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.datenschutz-info h4 {
  color: var(--gray-800);
  margin: 1rem 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 500;
}

.datenschutz-info ul {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.datenschutz-info li {
  margin-bottom: 0.5rem;
  color: var(--gray-700);
}

/* Placeholder Page */
.placeholder-info {
  text-align: center;
  font-size: 1rem;
  line-height: 1.6;
}

/* Clean Back Button Styles */
.back-button-container {
  margin-bottom: 2rem;
  text-align: left;
}

.back-button {
  background: var(--gray-100);
  color: var(--gray-700);
  border: 1px solid var(--gray-300);
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.back-button:hover {
  background: var(--gray-200);
  color: var(--gray-900);
}

/* Clean Footer Styles */
.footer {
  background: var(--gray-100);
  color: var(--gray-700);
  padding: 2rem 0;
  margin-top: auto;
  border-top: 1px solid var(--gray-200);
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.footer-links {
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.footer-links a {
  color: var(--gray-600);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  padding: 0.25rem 0.5rem;
  transition: color 0.2s ease;
}

.footer-links a:hover {
  color: var(--gray-900);
}

.footer-links span {
  color: var(--gray-400);
  font-weight: 300;
}

.footer-text p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--gray-500);
  font-weight: 400;
}

/* Clean Mobile Floating Action Button */
.floating-action-btn {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 60px;
  height: 60px;
  background: var(--tc-yellow-orange);
  border: none;
  border-radius: 50%;
  box-shadow: var(--shadow-lg);
  cursor: pointer;
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  color: var(--white);
  transition: all 0.2s ease;
}

.floating-action-btn:hover {
  background: var(--tc-yellow-orange-dark);
  box-shadow: var(--shadow-xl);
}

.fab-text {
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Hide FAB on desktop - only show on mobile/tablet */
@media (min-width: 1024px) {
  .floating-action-btn {
    display: none;
  }
}

/* Adjust FAB size for smaller screens */
@media (max-width: 480px) {
  .floating-action-btn {
    width: 56px;
    height: 56px;
    bottom: 1.5rem;
    right: 1.5rem;
  }

  .fab-text {
    font-size: 0.65rem;
  }
}

/* Touch and Mobile Improvements */
* {
  -webkit-tap-highlight-color: transparent;
}

/* Improved Touch Targets for Mobile */
@media (max-width: 768px) {
  .nav-list a {
    padding: 1rem 1.5rem;
    font-size: 1.1rem;
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .booking-button {
    padding: 1rem 2rem !important;
    font-size: 1.1rem !important;
    min-height: 52px !important;
  }

  .mobile-menu-toggle {
    min-width: 48px;
    min-height: 48px;
  }

  /* Better mobile content spacing */
  .content {
    padding: 2rem 1.5rem;
    margin: 0.5rem;
  }

  .welcome-message {
    padding: 2.5rem 2rem;
  }

  .news-section {
    padding: 2rem 1.5rem;
  }

  .news-item {
    padding: 1.5rem;
  }

  /* Improved mobile forms */
  .form-input {
    font-size: 16px; /* Prevents zoom on iOS */
    min-height: 48px;
  }

  /* Better mobile buttons */
  .btn {
    min-height: 48px;
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }

  .back-button {
    min-height: 48px;
    padding: 1rem 1.5rem;
    font-size: 1rem;
  }
}

/* Extra small mobile devices */
@media (max-width: 480px) {
  .header-content {
    padding: 0 1rem;
  }

  .logo h1 {
    font-size: 1.8rem;
  }

  .welcome-message h2 {
    font-size: 2.5rem;
  }

  .welcome-subtitle {
    font-size: 1rem;
  }

  .content {
    padding: 1.5rem 1rem;
    margin: 0.25rem;
  }

  .news-grid {
    gap: 1rem;
  }

  .news-item {
    padding: 1.25rem;
  }
}

/* Landscape mobile optimization */
@media (max-height: 500px) and (orientation: landscape) {
  .welcome-message {
    padding: 1.5rem;
  }

  .welcome-message h2 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }

  .news-section {
    padding: 1.5rem;
  }

  .floating-action-btn {
    width: 52px;
    height: 52px;
    bottom: 1rem;
    right: 1rem;
  }
}

/* Clean Mobile Optimizations */
@media (max-width: 768px) {
  /* Add padding-top to body to account for fixed header */
  body {
    padding-top: 70px;
  }

  .header-content {
    justify-content: space-between;
    padding: 0.75rem 1rem;
  }

  .logo {
    gap: 0.75rem;
  }

  .logo-img {
    height: 40px;
    width: 40px;
  }

  .logo h1 {
    font-size: 1.5rem;
  }

  /* Hide desktop navigation on mobile */
  .desktop-nav {
    display: none !important;
  }

  /* Show hamburger menu on mobile */
  .mobile-menu-toggle {
    display: flex !important;
  }

  /* Clean mobile navigation */
  .mobile-nav {
    display: block;
    position: fixed;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--white);
    border-top: 1px solid var(--gray-200);
    transform: translateY(-100%);
    transition: transform 0.3s ease;
    box-shadow: var(--shadow-lg);
    max-height: calc(100vh - 70px);
    overflow-y: auto;
  }

  /* Show navigation when open */
  .mobile-nav.nav-open {
    transform: translateY(0);
  }

  .nav-list {
    flex-direction: column;
    gap: 0;
    padding: 1rem 0;
    margin: 0;
    width: 100%;
  }

  .nav-list li {
    width: 100%;
    border-bottom: 1px solid var(--gray-100);
  }

  .nav-list li:last-child {
    border-bottom: none;
  }

  .nav-list a {
    display: block;
    padding: 1rem 1.5rem;
    font-size: 1rem;
    width: 100%;
    text-align: left;
    border-radius: 0;
    color: var(--gray-700);
    transition: background-color 0.2s ease;
  }

  .nav-list a:hover,
  .nav-list a.active {
    background-color: var(--gray-50);
    color: var(--gray-900);
  }

  .booking-button {
    background: var(--tc-yellow-orange) !important;
    color: var(--white) !important;
    font-weight: 600 !important;
  }

  .booking-button:hover {
    background: var(--tc-yellow-orange-dark) !important;
  }



  /* Clean mobile dropdown */
  .dropdown-menu {
    position: static;
    opacity: 0;
    visibility: hidden;
    max-height: 0;
    overflow: hidden;
    transform: none;
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    margin: 0.5rem 0;
    transition: all 0.2s ease;
  }

  /* Show dropdown on click (mobile) */
  .dropdown-open .dropdown-menu {
    opacity: 1;
    visibility: visible;
    max-height: 300px;
  }

  .dropdown-menu a {
    padding: 0.75rem 1.5rem !important;
    padding-left: 2rem !important;
    font-size: 0.95rem !important;
    color: var(--gray-700) !important;
    font-weight: 500 !important;
    text-align: left !important;
    border-bottom: 1px solid var(--gray-200);
    transition: all 0.2s ease !important;
  }

  .dropdown-menu a:last-child {
    border-bottom: none;
  }

  .dropdown-menu a:hover {
    background-color: var(--gray-100) !important;
    color: var(--gray-900) !important;
  }

  /* Mobile Home Page */
  .home-content {
    padding: 1rem;
    gap: 1.5rem;
  }

  .welcome-message {
    padding: 2rem 1.5rem;
  }

  .welcome-message h2 {
    font-size: 2rem;
  }

  .welcome-subtitle {
    font-size: 1rem;
  }

  .news-section {
    padding: 1.5rem;
  }

  .news-section h3 {
    font-size: 1.6rem;
    margin-bottom: 1rem;
  }

  .news-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .news-item {
    padding: 1.2rem;
  }

  .news-item h4 {
    font-size: 1.1rem;
  }

  .content {
    padding: 1rem;
    margin: 0.5rem;
    border-radius: 15px;
  }

  .anlage-images {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  /* Mobile Google Maps */
  .google-maps {
    margin-top: 1.5rem;
    border-radius: var(--radius-md);
  }

  .google-maps iframe {
    height: 250px;
  }

  .map-info {
    padding: 0.75rem;
  }

  .map-description {
    font-size: 0.9rem;
    flex-direction: column;
    gap: 0.25rem;
  }



  /* Mobile Footer */
  .footer-content {
    padding: 0 1rem;
  }

  .footer-links {
    gap: 0.5rem;
    font-size: 0.9rem;
  }

  .footer-text p {
    font-size: 0.8rem;
  }

  /* Mobile page optimization */
  .page {
    background-attachment: scroll;
    padding: 2rem 0;
  }

  .page::before {
    background: rgba(255, 255, 255, 0.95);
  }

  /* Mobile back button */
  .back-button {
    font-size: 0.9rem;
    padding: 0.7rem 1.2rem;
    width: 100%;
    justify-content: center;
    margin-bottom: 1rem;
  }
}

@media (max-width: 480px) {
  .logo h1 {
    font-size: 1.25rem;
  }

  .welcome-message {
    padding: 2rem 1rem;
  }

  .welcome-message h2 {
    font-size: 2rem;
  }

  .welcome-subtitle {
    font-size: 1rem;
  }
}
