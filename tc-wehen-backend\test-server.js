const express = require('express');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Basic middleware
app.use(cors({
  origin: process.env.CORS_ORIGIN || '*',
  credentials: true
}));
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'TC-Wehen Backend is running!' });
});

// Test route
app.get('/api/test', (req, res) => {
  res.json({ message: 'API is working!' });
});

// Simple routes without parameters first
app.get('/api/courts', (req, res) => {
  res.json({ message: 'Courts endpoint working!' });
});

app.get('/api/bookings', (req, res) => {
  res.json({ message: 'Bookings endpoint working!' });
});

// Error handling
app.use((err, req, res, next) => {
  console.error('Error:', err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

app.listen(PORT, () => {
  console.log(`🚀 Test Backend running on port ${PORT}`);
});

module.exports = app;
