const express = require('express');
const { supabase } = require('../config/supabase');
const { authenticateUser, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// Profil aktualisieren
router.put('/profile', authenticateUser, async (req, res) => {
  try {
    const { full_name, phone, membership_number } = req.body;

    const { data: profile, error } = await supabase
      .from('profiles')
      .update({
        full_name,
        phone,
        membership_number,
        updated_at: new Date().toISOString()
      })
      .eq('id', req.user.id)
      .select()
      .single();

    if (error) {
      return res.status(400).json({ error: error.message });
    }

    res.json({
      message: 'Profile updated successfully',
      profile
    });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({ error: 'Failed to update profile' });
  }
});

// Alle Benutzer abrufen (nur für <PERSON>mins)
router.get('/', authenticateUser, requireAdmin, async (req, res) => {
  try {
    const { data: profiles, error } = await supabase
      .from('profiles')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      return res.status(400).json({ error: error.message });
    }

    res.json(profiles);
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

// Benutzer zu Admin machen (nur für Admins)
router.put('/:id/makeadmin', authenticateUser, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { is_admin } = req.body;

    const { data: profile, error } = await supabase
      .from('profiles')
      .update({
        is_admin: is_admin,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      return res.status(400).json({ error: error.message });
    }

    res.json({
      message: `User ${is_admin ? 'promoted to' : 'removed from'} admin successfully`,
      profile
    });
  } catch (error) {
    console.error('Update admin status error:', error);
    res.status(500).json({ error: 'Failed to update admin status' });
  }
});

module.exports = router;
