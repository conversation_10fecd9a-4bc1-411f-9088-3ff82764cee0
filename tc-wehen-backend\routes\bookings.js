const express = require('express');
const { supabase } = require('../config/supabase');
const { authenticateUser } = require('../middleware/auth');

const router = express.Router();

// Test route
router.get('/test', (req, res) => {
  res.json({ message: 'Bookings API is working!' });
});

// Alle Buchungen abrufen (für einen bestimmten Tag/Platz)
router.get('/', async (req, res) => {
  try {
    const { date, court_id } = req.query;
    
    let query = supabase
      .from('bookings')
      .select(`
        *,
        courts (
          id,
          name,
          description
        )
      `)
      .eq('status', 'confirmed')
      .order('start_time');

    if (date) {
      query = query.eq('booking_date', date);
    }

    if (court_id) {
      query = query.eq('court_id', court_id);
    }

    const { data: bookings, error } = await query;

    if (error) {
      return res.status(400).json({ error: error.message });
    }

    res.json(bookings);
  } catch (error) {
    console.error('Get bookings error:', error);
    res.status(500).json({ error: 'Failed to fetch bookings' });
  }
});

// Neue Buchung erstellen
router.post('/', authenticateUser, async (req, res) => {
  try {
    const { court_id, booking_date, start_time, end_time, player1_name, player2_name } = req.body;

    if (!court_id || !booking_date || !start_time || !end_time || !player1_name) {
      return res.status(400).json({ 
        error: 'Court ID, booking date, start time, end time, and player 1 name are required' 
      });
    }

    // Prüfen ob der Zeitslot bereits gebucht ist
    const { data: existingBooking, error: checkError } = await supabase
      .from('bookings')
      .select('id')
      .eq('court_id', court_id)
      .eq('booking_date', booking_date)
      .eq('start_time', start_time)
      .eq('status', 'confirmed')
      .single();

    if (existingBooking) {
      return res.status(409).json({ error: 'This time slot is already booked' });
    }

    const { data: booking, error } = await supabase
      .from('bookings')
      .insert([{
        court_id,
        user_id: req.user.id,
        booking_date,
        start_time,
        end_time,
        player1_name,
        player2_name: player2_name || null,
        status: 'confirmed'
      }])
      .select(`
        *,
        courts (
          id,
          name,
          description
        )
      `)
      .single();

    if (error) {
      return res.status(400).json({ error: error.message });
    }

    res.status(201).json({
      message: 'Booking created successfully',
      booking
    });
  } catch (error) {
    console.error('Create booking error:', error);
    res.status(500).json({ error: 'Failed to create booking' });
  }
});

// Meine Buchungen abrufen (MUSS VOR /:id stehen!)
router.get('/mybookings', authenticateUser, async (req, res) => {
  try {
    const { data: bookings, error } = await supabase
      .from('bookings')
      .select(`
        *,
        courts (
          id,
          name,
          description
        )
      `)
      .eq('user_id', req.user.id)
      .eq('status', 'confirmed')
      .order('booking_date', { ascending: true })
      .order('start_time', { ascending: true });

    if (error) {
      return res.status(400).json({ error: error.message });
    }

    res.json(bookings);
  } catch (error) {
    console.error('Get my bookings error:', error);
    res.status(500).json({ error: 'Failed to fetch your bookings' });
  }
});

// Buchung aktualisieren
router.put('/:id', authenticateUser, async (req, res) => {
  try {
    const { id } = req.params;
    const { player1_name, player2_name } = req.body;

    const { data: booking, error } = await supabase
      .from('bookings')
      .update({
        player1_name,
        player2_name: player2_name || null,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('user_id', req.user.id)
      .select(`
        *,
        courts (
          id,
          name,
          description
        )
      `)
      .single();

    if (error) {
      return res.status(400).json({ error: error.message });
    }

    if (!booking) {
      return res.status(404).json({ error: 'Booking not found or not authorized' });
    }

    res.json({
      message: 'Booking updated successfully',
      booking
    });
  } catch (error) {
    console.error('Update booking error:', error);
    res.status(500).json({ error: 'Failed to update booking' });
  }
});

// Buchung löschen
router.delete('/:id', authenticateUser, async (req, res) => {
  try {
    const { id } = req.params;

    const { data: booking, error } = await supabase
      .from('bookings')
      .delete()
      .eq('id', id)
      .eq('user_id', req.user.id)
      .select()
      .single();

    if (error) {
      return res.status(400).json({ error: error.message });
    }

    if (!booking) {
      return res.status(404).json({ error: 'Booking not found or not authorized' });
    }

    res.json({ message: 'Booking deleted successfully' });
  } catch (error) {
    console.error('Delete booking error:', error);
    res.status(500).json({ error: 'Failed to delete booking' });
  }
});

module.exports = router;
