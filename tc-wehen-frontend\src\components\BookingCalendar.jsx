import { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { supabase } from '../lib/supabase'
import { Calendar, Clock, User, Plus, X } from 'lucide-react'
import './BookingCalendar.css'

const BookingCalendar = () => {
  const { user } = useAuth()
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])
  const [courts, setCourts] = useState([])
  const [bookings, setBookings] = useState([])
  const [loading, setLoading] = useState(true)
  const [showBookingForm, setShowBookingForm] = useState(false)
  const [selectedSlot, setSelectedSlot] = useState(null)
  const [bookingForm, setBookingForm] = useState({
    player1_name: '',
    player2_name: ''
  })

  // Zeitslots von 9:00 bis 21:00
  const timeSlots = []
  for (let hour = 9; hour <= 20; hour++) {
    timeSlots.push(`${hour.toString().padStart(2, '0')}:00`)
  }

  useEffect(() => {
    fetchCourts()
  }, [])

  useEffect(() => {
    if (selectedDate) {
      fetchBookings()
    }
  }, [selectedDate])

  const fetchCourts = async () => {
    try {
      const { data, error } = await supabase
        .from('courts')
        .select('*')
        .eq('is_active', true)
        .order('id')

      if (error) throw error
      setCourts(data || [])
    } catch (error) {
      console.error('Error fetching courts:', error)
    }
  }

  const fetchBookings = async () => {
    setLoading(true)
    try {
      const { data, error } = await supabase
        .from('bookings')
        .select(`
          *,
          courts (
            id,
            name,
            description
          )
        `)
        .eq('booking_date', selectedDate)
        .eq('status', 'confirmed')
        .order('start_time')

      if (error) throw error
      setBookings(data || [])
    } catch (error) {
      console.error('Error fetching bookings:', error)
    } finally {
      setLoading(false)
    }
  }

  const isSlotBooked = (courtId, timeSlot) => {
    return bookings.some(booking => 
      booking.court_id === courtId && booking.start_time === timeSlot + ':00'
    )
  }

  const getBookingForSlot = (courtId, timeSlot) => {
    return bookings.find(booking => 
      booking.court_id === courtId && booking.start_time === timeSlot + ':00'
    )
  }

  const handleSlotClick = (courtId, timeSlot) => {
    if (isSlotBooked(courtId, timeSlot)) return
    
    // Prüfe ob das Datum in der Vergangenheit liegt
    const slotDateTime = new Date(`${selectedDate}T${timeSlot}:00`)
    if (slotDateTime < new Date()) {
      alert('Buchungen in der Vergangenheit sind nicht möglich')
      return
    }

    setSelectedSlot({ courtId, timeSlot })
    setShowBookingForm(true)
  }

  const handleBooking = async (e) => {
    e.preventDefault()
    if (!selectedSlot || !bookingForm.player1_name) return

    try {
      const { data: session } = await supabase.auth.getSession()
      if (!session?.session?.access_token) {
        alert('Sie müssen angemeldet sein')
        return
      }

      const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}/api/bookings`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.session.access_token}`
        },
        body: JSON.stringify({
          court_id: selectedSlot.courtId,
          booking_date: selectedDate,
          start_time: selectedSlot.timeSlot + ':00',
          end_time: (parseInt(selectedSlot.timeSlot) + 1).toString().padStart(2, '0') + ':00:00',
          player1_name: bookingForm.player1_name,
          player2_name: bookingForm.player2_name || null
        })
      })

      if (response.ok) {
        setShowBookingForm(false)
        setSelectedSlot(null)
        setBookingForm({ player1_name: '', player2_name: '' })
        fetchBookings()
        alert('Buchung erfolgreich!')
      } else {
        const error = await response.json()
        alert(error.error || 'Fehler bei der Buchung')
      }
    } catch (error) {
      console.error('Booking error:', error)
      alert('Fehler bei der Buchung')
    }
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('de-DE', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <div className="booking-container">
      {/* Mobile-First Header */}
      <div className="booking-header">
        <h1 className="booking-title">Tennisplatz buchen</h1>
        <p className="booking-subtitle">Wählen Sie Datum und Uhrzeit für Ihren Tennisplatz</p>
      </div>

      {/* Mobile Date Selector */}
      <div className="mobile-date-selector">
        <div className="date-navigation">
          <button
            className="date-nav-btn"
            onClick={() => {
              const prevDate = new Date(selectedDate)
              prevDate.setDate(prevDate.getDate() - 1)
              if (prevDate >= new Date().setHours(0,0,0,0)) {
                setSelectedDate(prevDate.toISOString().split('T')[0])
              }
            }}
            disabled={selectedDate <= new Date().toISOString().split('T')[0]}
          >
            ←
          </button>

          <div className="current-date">
            <div className="date-display">
              <Calendar size={20} />
              <span className="date-text">{formatDate(selectedDate)}</span>
            </div>
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="date-input-hidden"
              min={new Date().toISOString().split('T')[0]}
            />
          </div>

          <button
            className="date-nav-btn"
            onClick={() => {
              const nextDate = new Date(selectedDate)
              nextDate.setDate(nextDate.getDate() + 1)
              setSelectedDate(nextDate.toISOString().split('T')[0])
            }}
          >
            →
          </button>
        </div>
      </div>

      {loading ? (
        <div className="loading-state">
          <div className="loading-spinner"></div>
          <p>Lade verfügbare Zeiten...</p>
        </div>
      ) : (
        <div className="courts-container">
          {courts.map(court => (
            <div key={court.id} className="court-card">
              <div className="court-header">
                <h3 className="court-name">{court.name}</h3>
                <span className="court-description">{court.description}</span>
              </div>

              <div className="time-slots-grid">
                {timeSlots.map(timeSlot => {
                  const isBooked = isSlotBooked(court.id, timeSlot)
                  const booking = getBookingForSlot(court.id, timeSlot)
                  const isPast = new Date(`${selectedDate}T${timeSlot}:00`) < new Date()

                  return (
                    <div
                      key={timeSlot}
                      className={`time-slot ${isBooked ? 'booked' : ''} ${isPast ? 'past' : ''}`}
                      onClick={() => !isBooked && !isPast && handleSlotClick(court.id, timeSlot)}
                    >
                      <div className="time-display">
                        <Clock size={16} />
                        <span className="time-text">{timeSlot}</span>
                      </div>

                      {isBooked ? (
                        <div className="booking-info">
                          <div className="booking-status">Gebucht</div>
                          <div className="booking-players">
                            {booking?.player1_name}
                            {booking?.player2_name && ` vs ${booking.player2_name}`}
                          </div>
                        </div>
                      ) : isPast ? (
                        <div className="slot-status past-status">
                          Vergangen
                        </div>
                      ) : (
                        <div className="slot-status available-status">
                          <Plus size={20} />
                          <span>Verfügbar</span>
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Mobile-Optimized Booking Modal */}
      {showBookingForm && (
        <div className="booking-modal-overlay">
          <div className="booking-modal">
            <div className="modal-header">
              <h3 className="modal-title">Tennisplatz buchen</h3>
              <button
                onClick={() => setShowBookingForm(false)}
                className="modal-close-btn"
              >
                <X size={24} />
              </button>
            </div>

            <div className="booking-summary">
              <div className="summary-item">
                <span className="summary-label">Datum:</span>
                <span className="summary-value">{formatDate(selectedDate)}</span>
              </div>
              <div className="summary-item">
                <span className="summary-label">Zeit:</span>
                <span className="summary-value">{selectedSlot?.timeSlot} Uhr</span>
              </div>
              <div className="summary-item">
                <span className="summary-label">Platz:</span>
                <span className="summary-value">{courts.find(c => c.id === selectedSlot?.courtId)?.name}</span>
              </div>
            </div>

            <form onSubmit={handleBooking} className="booking-form">
              <div className="form-group">
                <label className="form-label">
                  <User size={18} />
                  Spieler 1 (Pflichtfeld)
                </label>
                <input
                  type="text"
                  value={bookingForm.player1_name}
                  onChange={(e) => setBookingForm({
                    ...bookingForm,
                    player1_name: e.target.value
                  })}
                  className="form-input mobile-input"
                  placeholder="Ihr Name"
                  required
                  autoComplete="name"
                  autoFocus
                />
              </div>

              <div className="form-group">
                <label className="form-label">
                  <User size={18} />
                  Spieler 2 (Optional)
                </label>
                <input
                  type="text"
                  value={bookingForm.player2_name}
                  onChange={(e) => setBookingForm({
                    ...bookingForm,
                    player2_name: e.target.value
                  })}
                  className="form-input mobile-input"
                  placeholder="Name des Mitspielers"
                  autoComplete="name"
                />
              </div>

              <div className="modal-actions">
                <button
                  type="button"
                  onClick={() => setShowBookingForm(false)}
                  className="btn btn-outline mobile-btn"
                >
                  Abbrechen
                </button>
                <button
                  type="submit"
                  className="btn btn-tennis mobile-btn"
                >
                  Jetzt buchen
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}

export default BookingCalendar
